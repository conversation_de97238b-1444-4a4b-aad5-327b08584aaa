#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import shutil
import re
from pathlib import Path

def replace_in_file(file_path, old_pattern, new_pattern):
    """替换文件中的内容"""
    try:
        with open(file_path, 'r', encoding='utf-8') as file:
            content = file.read()

        new_content = re.sub(old_pattern, new_pattern, content)

        if new_content != content:
            with open(file_path, 'w', encoding='utf-8') as file:
                file.write(new_content)
            print(f"已修改文件: {file_path}")
    except Exception as e:
        print(f"处理文件 {file_path} 时出错: {str(e)}")

def move_package_directory(base_dir, old_package, new_package):
    """移动包目录"""
    try:
        old_path = os.path.join(base_dir, *old_package.split('.'))
        new_path = os.path.join(base_dir, *new_package.split('.'))

        if os.path.exists(old_path):
            # 确保新目录的父目录存在
            os.makedirs(os.path.dirname(new_path), exist_ok=True)

            # 如果新目录已存在，先将旧目录中的内容复制过去
            if os.path.exists(new_path):
                for item in os.listdir(old_path):
                    old_item = os.path.join(old_path, item)
                    new_item = os.path.join(new_path, item)
                    if os.path.isdir(old_item):
                        shutil.copytree(old_item, new_item, dirs_exist_ok=True)
                    else:
                        shutil.copy2(old_item, new_item)
            else:
                # 如果新目录不存在，直接移动
                shutil.move(old_path, new_path)

            # 删除旧的空目录
            try:
                os.removedirs(os.path.dirname(old_path))
            except OSError:
                pass

            print(f"已移动包目录: {old_path} -> {new_path}")
    except Exception as e:
        print(f"移动目录时出错: {str(e)}")

def replace_package_references(project_dir, old_package, new_package):
    """替换所有文件中的包引用"""
    # 定义要处理的文件类型
    file_patterns = ['*.java', '*.xml', '*.yaml', '*.properties', '*.vm']

    for pattern in file_patterns:
        for file_path in Path(project_dir).rglob(pattern):
            replace_in_file(str(file_path), old_package, new_package)

def main():
    # 项目根目录
    project_dir = os.path.dirname(os.path.abspath(__file__))

    # 旧包名和新包名
    old_package = "net.lab1024"
    new_package = "cn.edu.xmut"

    print("开始替换包名...")

    # 1. 移动源代码目录
    src_dirs = []
    for root, dirs, files in os.walk(project_dir):
        if 'src/main/java' in root or 'src/test/java' in root:
            src_dirs.append(root)

    for src_dir in src_dirs:
        move_package_directory(src_dir, old_package, new_package)

    # 2. 替换所有文件中的包引用
    replace_package_references(project_dir, old_package, new_package)

    # 3. 替换 pom.xml 中的 groupId
    pom_files = Path(project_dir).rglob('pom.xml')
    for pom_file in pom_files:
        replace_in_file(
            str(pom_file),
            f"<groupId>{old_package}</groupId>",
            f"<groupId>{new_package}</groupId>"
        )

    print("包名替换完成！")

if __name__ == "__main__":
    main()
