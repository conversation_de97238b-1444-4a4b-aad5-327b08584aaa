<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.lab1024.sa.admin.module.business.goods.dao.GoodsDao">
    <update id="batchUpdateDeleted">
        update t_goods
        set deleted_flag = #{deletedFlag}
        WHERE goods_id IN
        <foreach collection="goodsIdList" open="(" close=")" separator="," item="item">
            #{item}
        </foreach>
    </update>

    <!-- 分页 查询商品 -->
    <select id="query" resultType="net.lab1024.sa.admin.module.business.goods.domain.vo.GoodsVO">
        SELECT * FROM t_goods
        <where>
            <if test="query.searchWord != null and query.searchWord !=''">
                INSTR(goods_name,#{query.searchWord})
            </if>
            <if test="query.place != null">
                AND INSTR(place,#{query.place})
            </if>
            <if test="query.goodsStatus != null">
                AND goods_status = #{query.goodsStatus}
            </if>
            <if test="query.categoryId != null">
                AND category_id = #{query.categoryId}
            </if>
            <if test="query.shelvesFlag != null">
                AND shelves_flag = #{query.shelvesFlag}
            </if>
            <if test="query.deletedFlag != null">
                AND deleted_flag = #{query.deletedFlag}
            </if>
        </where>
        <if test="query.sortItemList == null or query.sortItemList.size == 0">
            ORDER BY goods_id DESC
        </if>
    </select>

</mapper>