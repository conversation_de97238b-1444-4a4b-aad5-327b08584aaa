<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.lab1024.sa.admin.module.business.oa.invoice.dao.InvoiceDao">
    <update id="deleteInvoice">
        UPDATE t_oa_invoice
        SET deleted_flag = #{deletedFlag}
        WHERE invoice_id = #{invoiceId}
    </update>
    <select id="queryByAccountNumber"
            resultType="net.lab1024.sa.admin.module.business.oa.invoice.domain.InvoiceEntity">
        SELECT *
        FROM t_oa_invoice
        WHERE enterprise_id = #{enterpriseId}
        AND account_number = #{accountNumber}
        AND deleted_flag = #{deletedFlag}
        <if test="excludeInvoiceId != null">
            AND invoice_id != #{excludeInvoiceId}
        </if>
    </select>
    <select id="queryPage" resultType="net.lab1024.sa.admin.module.business.oa.invoice.domain.InvoiceVO">
        SELECT t_oa_invoice.*,
        t_oa_enterprise.enterprise_name
        FROM t_oa_invoice 
        LEFT JOIN t_oa_enterprise  ON t_oa_invoice.enterprise_id = t_oa_enterprise.enterprise_id
        <where>
            t_oa_invoice.deleted_flag = #{queryForm.deletedFlag}
            <if test="queryForm.keywords != null and queryForm.keywords != ''">
                AND (INSTR(t_oa_invoice.invoice_heads,#{queryForm.keywords}) OR INSTR(t_oa_invoice.account_number,#{queryForm.keywords}) OR
                INSTR(t_oa_invoice.create_user_name,#{queryForm.keywords}))
            </if>
            <if test="queryForm.startTime != null">
                AND DATE_FORMAT(t_oa_invoice.create_time, '%Y-%m-%d') &gt;= #{queryForm.startTime}
            </if>
            <if test="queryForm.endTime != null">
                AND DATE_FORMAT(t_oa_invoice.create_time, '%Y-%m-%d') &lt;= #{queryForm.endTime}
            </if>
            <if test="queryForm.disabledFlag != null">
                AND t_oa_invoice.disabled_flag = #{queryForm.disabledFlag}
            </if>
            <if test="queryForm.enterpriseId != null">
                AND t_oa_invoice.enterprise_id = #{queryForm.enterpriseId}
            </if>
        </where>
        <if test="queryForm.sortItemList == null or queryForm.sortItemList.size == 0">
            ORDER BY t_oa_invoice.create_time DESC
        </if>
    </select>
    <select id="getDetail" resultType="net.lab1024.sa.admin.module.business.oa.invoice.domain.InvoiceVO">
        SELECT t_oa_invoice.*,
               t_oa_enterprise.enterprise_name
        FROM t_oa_invoice 
        LEFT JOIN t_oa_enterprise  ON t_oa_invoice.enterprise_id = t_oa_enterprise.enterprise_id
        WHERE t_oa_invoice.invoice_id = #{invoiceId}
          AND t_oa_invoice.deleted_flag = #{deletedFlag}
    </select>
</mapper>