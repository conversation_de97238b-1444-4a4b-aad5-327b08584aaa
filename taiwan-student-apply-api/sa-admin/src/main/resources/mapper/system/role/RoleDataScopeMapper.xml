<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.lab1024.sa.admin.module.system.role.dao.RoleDataScopeDao">

    <resultMap id="DataScopeRoleEntity" type="net.lab1024.sa.admin.module.system.role.domain.entity.RoleDataScopeEntity"></resultMap>

    <select id="listByRoleId" resultMap="DataScopeRoleEntity">
        select id,data_scope_type,view_type,role_id,update_time,create_time from t_role_data_scope where role_id = #{roleId}
    </select>

    <select id="listByRoleIdList" resultMap="DataScopeRoleEntity">
        select id,data_scope_type,view_type,role_id,update_time,create_time from t_role_data_scope where role_id IN
        <foreach collection="roleIdList" item="item" open="(" close= ")"  separator=",">
            #{item}
        </foreach>
    </select>

    <delete id="deleteByRoleId">
        DELETE FROM t_role_data_scope
        WHERE role_id = #{roleId}
    </delete>


</mapper>