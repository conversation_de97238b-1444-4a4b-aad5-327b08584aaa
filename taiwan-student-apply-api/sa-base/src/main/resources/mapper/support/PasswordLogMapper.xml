<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.lab1024.sa.base.module.support.securityprotect.dao.PasswordLogDao">

    <select id="selectLastByUserTypeAndUserId"
            resultType="net.lab1024.sa.base.module.support.securityprotect.domain.PasswordLogEntity">
        select
            *
        from t_password_log
        where
            user_id = #{userId}
          and user_type = #{userType}
        order by id desc
            limit 1
    </select>

    <select id="selectOldPassword" resultType="java.lang.String">
        select
            new_password
        from t_password_log
        where
            user_id = #{userId}
          and user_type = #{userType}
        order by id desc
            limit #{limit}
    </select>

</mapper>