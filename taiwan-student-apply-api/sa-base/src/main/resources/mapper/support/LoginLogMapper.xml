<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.lab1024.sa.base.module.support.loginlog.LoginLogDao">

    <select id="queryByPage" resultType="net.lab1024.sa.base.module.support.loginlog.domain.LoginLogVO">
        select
        *
        from t_login_log
        <where>
            <if test="query.userId != null">
                AND user_id = #{query.userId}
            </if>
            <if test="query.userType != null">
                AND user_type = #{query.userType}
            </if>
            <if test="query.ip != null and query.ip != ''">
                AND INSTR(login_ip,#{query.ip})
            </if>
            <if test="query.startDate != null and query.startDate != ''">
                AND DATE_FORMAT(create_time, '%Y-%m-%d') &gt;= #{query.startDate}
            </if>
            <if test="query.endDate != null and query.endDate != ''">
                AND DATE_FORMAT(create_time, '%Y-%m-%d') &lt;= #{query.endDate}
            </if>
            <if test="query.userName != null and query.userName != ''">
                AND INSTR(user_name,#{query.userName})
            </if>
        </where>
        order by create_time desc
    </select>

    <select id="queryLastByUserId" resultType="net.lab1024.sa.base.module.support.loginlog.domain.LoginLogVO">
        select
        *
        from t_login_log
        where
            user_id = #{userId}
            and user_type = #{userType}
            and login_result = #{loginLogResult}
        order by login_log_id desc
        limit 1
    </select>

</mapper>