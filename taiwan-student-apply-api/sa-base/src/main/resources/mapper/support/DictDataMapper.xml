<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.lab1024.sa.base.module.support.dict.dao.DictDataDao">


    <select id="queryByDictId" resultType="net.lab1024.sa.base.module.support.dict.domain.vo.DictDataVO">
        select *
        from t_dict_data
        where dict_id = #{dictId}
        order by sort_order desc
    </select>

    <select id="getAll" resultType="net.lab1024.sa.base.module.support.dict.domain.vo.DictDataVO">
        select t_dict_data.*,
               t_dict.dict_code
        from t_dict_data
                 left join t_dict on t_dict_data.dict_id = t_dict.dict_id
        order by t_dict_data.sort_order desc
    </select>


    <select id="selectByDictIdAndValue"
            resultType="net.lab1024.sa.base.module.support.dict.domain.entity.DictDataEntity">
        select *
        from t_dict_data
        where dict_id = #{dictId}
          and data_value = #{dataValue}
    </select>

    <select id="selectByDictDataIds" resultType="net.lab1024.sa.base.module.support.dict.domain.vo.DictDataVO">
        select
        t_dict_data.*,
        t_dict.dict_code
        from t_dict_data
        left join t_dict on t_dict_data.dict_id = t_dict.dict_id
        <where>
            <if test="dictDataIdList != null and dictDataIdList.size > 0">
                and t_dict_data.dict_data_id in
                <foreach collection="dictDataIdList" open="(" close=")" item="item" separator=",">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>
</mapper>
