.login-container {
  width: 100%;
  height: 100%;
  background: url(/@/assets/images/login/login-bg.png) no-repeat center;
  background-size: cover;
  display: flex;
  align-items: center;
  justify-content: center;
  .box-item {
    width: 444px;
    height: 600px;
    &.desc {
      background: #ffffff;
      border-radius: 12px 0px 0px 12px;
      box-shadow: 0px 16px 73px 8px rgba(203, 203, 203, 0.2);
      padding: 23px 25px;
    }
    &.login {
      background: #ffffff;
      border-radius: 0px 12px 12px 0px;
      padding: 34px 42px;
      position: relative;
    }
    .login-qr {
      position: absolute;
      top: 0;
      right: 0;
      width: 66px;
      height: 66px;
    }
    .welcome {
      background: url(/@/assets/images/login/left-bg1.png) no-repeat center;
      background-size: cover;
      height: 100%;
      border-radius: 8px;
      box-shadow: 0px 6px 20px 0px rgba(33,47,117,0.10);
      padding-top: 35px;
      p{
        color: #333333;
        line-height: 25px;
        letter-spacing: 0.26px;
        text-align: center;
        font-weight: 700;
        font-size: 26px;
      }
      .sub-welcome{
        color: #333333;
        line-height: 25px;
        letter-spacing: 0.26px;
        font-weight: 700;
        font-size: 20px;
      }
    }
    .app-qr-box {
      display: flex;
      align-items: center;
      justify-content: space-around;
      margin-top: 20px;
      .app-qr {
        display: flex;
        align-items: center;
        justify-content: center;
        flex-direction: column;
        width: 50%;
        > img {
          width: 112px;
          height: 112px;
        }

        .qr-desc {
          display: flex;
          align-items: center;
          margin-top: 11px;
          font-size: 12px;
          font-weight: 500;
          text-align: center;
          color: #ffffff;
          > img {
            width: 15px;
            height: 18px;
            margin-right: 9px;
          }
        }

        .qr-desc-marquee {
          width: 100%;
          display: flex;
          align-items: center;
          margin-top: 11px;
          font-size: 12px;
          font-weight: 500;
          text-align: center;
          color: #ffffff;

          @keyframes marquee {
            0% {
              transform: translateX(0);
            }
            100% {
              transform: translateX(-30%);
            }
          }

          .marquee {
            flex: 1;
            overflow: hidden;
            span {
              display: inline-block;
              width: 100%;
              white-space: nowrap;
              animation: marquee 5s linear infinite;
            }
          }
        }
      }
    }
    .login-title {
      font-size: 20px;
      text-align: center;
      color: #1e1e1e;
      margin-bottom: 35px;
    }
    .login-form {
      .captcha-input {
        width: 60%;
      }

      .captcha-img {
        cursor: pointer;
      }
    }
    .ant-input,
    .ant-input-affix-wrapper {
      height: 44px;
      border: 1px solid #ededed;
      border-radius: 4px;
    }
    .code-btn{
      height: 44px;
      padding: 4px 5px;
      width: 108px;
    }

    .eye-box {
      position: absolute;
      right: 15px;
      top: 10px;
      .eye-icon {
        width: 20px;
        height: 20px;
        cursor: pointer;
      }
    }
    .btn {
      width: 350px;
      height: 50px;
      background: #1748FD;
      border-radius: 4px;
      font-size: 16px;
      font-weight: 700;
      text-align: center;
      color: #ffffff;
      line-height: 50px;
      cursor: pointer;
    }
  }

  .more {
    margin-top: 30px;
    .title-box {
      display: flex;
      align-items: center;
      justify-content: center;
      > p {
        margin-bottom: 0;
      }
    }
    .line {
      width: 114px;
      height: 1px;
      background: #e6e6e6;
    }
    .title {
      font-size: 14px;
      font-weight: 500;
      color: #a1aebe;
      margin: 0 19px;
    }
    .login-type {
      padding: 0 5px;
      margin-top: 25px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      > img {
        width: 30px;
        height: 30px;
      }
    }
  }
}
