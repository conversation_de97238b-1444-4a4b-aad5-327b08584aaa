/* 
  * OA发票信息
  * 
  * @Author:    善逸 
  * @Date:      2022-09-03 21:48:54 
  * @Wechat:    zhuda1024 
  * @Email:     <EMAIL> 
  * @Copyright  1024创新实验室 （ https://1024lab.net ），Since 2012 
  */
import { postRequest, getRequest } from '/@/lib/axios';

export const invoiceApi = {

  // 新建发票信息 <AUTHOR>
  create: (param) => {
    return postRequest('/oa/invoice/create', param);
  },

  // 删除发票信息 <AUTHOR>
  delete: (bankId) => {
    return getRequest(`/oa/invoice/delete/${bankId}`);
  },

  // 查询发票信息详情 <AUTHOR>
  detail: (bankId) => {
    return getRequest(`//oa/invoice/get/${bankId}`);
  },

  // 分页查询发票信息 <AUTHOR>
  pageQuery: (param) => {
    return postRequest('/oa/invoice/page/query', param);
  },

  // 编辑发票信息 <AUTHOR>
  update: (param) => {
    return postRequest('/oa/invoice/update', param);
  },

  // 查询发票列表 <AUTHOR>
  queryList: (enterpriseId) => {
    return getRequest(`/oa/invoice/query/list/${enterpriseId}`);
  },

};
